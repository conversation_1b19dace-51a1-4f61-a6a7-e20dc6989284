{"name": "@prisma/engines", "version": "6.9.0", "description": "This package is intended for Prisma's internal use", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/engines"}, "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "devDependencies": {"@swc/core": "1.11.5", "@swc/jest": "0.2.37", "@types/jest": "29.5.14", "@types/node": "18.19.76", "execa": "5.1.1", "typescript": "5.4.5", "vitest": "3.2.0"}, "dependencies": {"@prisma/engines-version": "6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e", "@prisma/debug": "6.9.0", "@prisma/fetch-engine": "6.9.0", "@prisma/get-platform": "6.9.0"}, "files": ["dist", "download", "scripts"], "sideEffects": false, "scripts": {"dev": "DEV=true tsx helpers/build.ts", "build": "tsx helpers/build.ts", "test": "vitest run", "postinstall": "node scripts/postinstall.js"}}